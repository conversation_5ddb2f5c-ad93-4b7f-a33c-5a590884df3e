{"name": "ai-attribution-tool", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.24.8", "axios": "^1.6.0", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.11.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "flatpickr": "^4.6.13", "moment": "^2.30.1", "react": "^18.2.0", "react-bootstrap": "^2.9.0", "react-dom": "^18.2.0", "react-flatpickr": "^3.10.13", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "reactflow": "^11.11.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "PORT=3000 react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.6"}}