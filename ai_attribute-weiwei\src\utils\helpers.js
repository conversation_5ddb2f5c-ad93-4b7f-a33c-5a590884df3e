// 日期格式转换函数
export const formatDateToYYYYMMDD = (dateInput) => {
  if (!dateInput) return '';

  // 如果是 Moment 或 Dayjs 对象（带有 format 方法）
  if (typeof dateInput.format === 'function') {
    return dateInput.format('YYYYMMDD');
  }
  
  // 如果是 Date 对象
  if (dateInput instanceof Date) {
    const year = dateInput.getFullYear();
    const month = String(dateInput.getMonth() + 1).padStart(2, '0');
    const day = String(dateInput.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }
  
  // 如果是字符串
  if (typeof dateInput === 'string') {
    // 如果已经是 YYYYMMDD 格式，直接返回
    if (/^\d{8}$/.test(dateInput)) {
      return dateInput;
    }
    // 如果是 YYYY-MM-DD 格式，移除连字符
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
      return dateInput.replace(/-/g, '');
    }
    // 如果是完整的日期时间字符串（如：Sun, 09 Feb 2025 16:00:00 GMT），尝试解析
    try {
      const parsedDate = new Date(dateInput);
      if (!isNaN(parsedDate.getTime())) {
        // 直接使用本地时间，避免 UTC 转换导致的日期偏移
        const year = parsedDate.getFullYear();
        const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
        const day = String(parsedDate.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
      }
    } catch (e) {
      // 如果解析失败，继续下面的处理
    }
  }
  
  // 如果无法识别格式，尝试转换为字符串并移除连字符
  const stringValue = String(dateInput);
  // 如果字符串包含日期时间格式，尝试解析
  if (stringValue.includes(',') || stringValue.includes('GMT') || stringValue.includes('UTC')) {
    try {
      const parsedDate = new Date(stringValue);
      if (!isNaN(parsedDate.getTime())) {
        // 直接使用本地时间，避免 UTC 转换导致的日期偏移
        const year = parsedDate.getFullYear();
        const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
        const day = String(parsedDate.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
      }
    } catch (e) {
      // 如果解析失败，使用原来的逻辑
    }
  }
  return stringValue.replace(/-/g, '');
};

// 日期范围验证和天数计算
export const calculateDateRange = (startDate, endDate) => {
  if (startDate && endDate) {
    // 检查是否为Moment对象
    const start = startDate._isAMomentObject ? startDate.toDate() : new Date(startDate);
    const end = endDate._isAMomentObject ? endDate.toDate() : new Date(endDate);

    if (start > end) {
      return { valid: false, message: "开始日期不能大于结束日期！" };
    } else {
      const timeDiff = Math.abs(end - start);
      const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // 包含结束日期
      return { valid: true, days: diffDays, message: `共${diffDays}天` };
    }
  }
  return { valid: true, days: 0, message: "" };
};

// 高亮百分比并格式化标题和粗体文本
export const highlightPercentages = (text) => {
  // 处理一级标题（如：### 一、优惠力度分析）
  text = text.replace(/#{1,3}\s+([一二三四五六七八九十]+、[^#\n]+)/g, 
    '<h1 style="font-size: 24px; margin-top: 20px; margin-bottom: 10px; font-weight: bold;">$1</h1>');
  
  // 处理粗体文本（如：**策略建议**）
  text = text.replace(/\*\*([^*]+)\*\*/g, '<span style="color: blue; font-weight: bold;">$1</span>');
  
  // 修改正则表达式，只匹配带元、%符号的数值，或者明确是独立的正负数
  return text.replace(/((?:^|\s|[^-\d]))([-+][\d,]+(\.\d+)?(元|%|¥))/g, (match, prefix, numPart) => {
    if (numPart.startsWith("-")) {
      return prefix + `<span style="color: red;">${numPart}</span>`;
    } else if (numPart.startsWith("+")) {
      return prefix + `<span style="color: green;">${numPart}</span>`;
    }
    return match;
  });
}; 