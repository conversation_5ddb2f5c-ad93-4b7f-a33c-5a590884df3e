body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: #f5f5f7;
  color: #1d1d1f;
}

.container {
  max-width: 1200px !important;
  margin: 40px auto !important;
  padding: 0 20px !important;
}

h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 30px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.form-control, .form-select {
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  height: 36px;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-control:focus, .form-select:focus {
  border-color: #0071e3;
  box-shadow: 0 0 0 3px rgba(0, 125, 250, 0.1);
  outline: none;
}

.btn {
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #0071e3;
  border: none;
  color: white;
}

.btn-primary:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
}

.btn-outline-primary {
  color: #0071e3;
  border: 1px solid #0071e3;
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: rgba(0, 125, 250, 0.1);
  color: #0071e3;
}

.output-box {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
}

.progress {
  height: 6px;
  border-radius: 3px;
  background-color: #e5e5e5;
}

.progress-bar {
  background-color: #0071e3;
  transition: width 0.3s ease;
}

.table {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table th {
  background-color: #f5f5f7;
  font-weight: 600;
  color: #1d1d1f;
  padding: 12px;
}

.table td {
  padding: 12px;
  border-bottom: 1px solid #f5f5f7;
}

.divider {
  height: 1px;
  background-color: #d2d2d7;
  margin: 20px 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.date-container, .compare-container, .filter-container {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  gap: 20px;
}

.sheet-container {
  margin-top: 20px;
}

.sheet-tabs {
  margin-bottom: 10px;
}

.hidden {
  display: none;
}

/* 表格响应式样式 */
.ant-table-wrapper {
  width: 100% !important;
  overflow: visible !important; /* 移除滚动条，让外层容器处理 */
}

.ant-table {
  width: auto !important;
  min-width: 100% !important;
  table-layout: auto !important;
}

.ant-table-content {
  overflow: visible !important; /* 移除滚动条，让外层容器处理 */
}

/* 表格横向滚动条样式优化 - 只为外层容器设置滚动条样式 */
.supply-side-table-container > div::-webkit-scrollbar,
.marketing-side-table-container > div::-webkit-scrollbar,
.table-content-container::-webkit-scrollbar {
  height: 8px;
}

.supply-side-table-container > div::-webkit-scrollbar-track,
.marketing-side-table-container > div::-webkit-scrollbar-track,
.table-content-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.supply-side-table-container > div::-webkit-scrollbar-thumb,
.marketing-side-table-container > div::-webkit-scrollbar-thumb,
.table-content-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.supply-side-table-container > div::-webkit-scrollbar-thumb:hover,
.marketing-side-table-container > div::-webkit-scrollbar-thumb:hover,
.table-content-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保所有表格容器都支持横向滚动 */
.supply-side-table-container > div,
.marketing-side-table-container > div {
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

/* 表头样式优化 */
.ant-table-thead > tr > th {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
  font-weight: bold !important;
  color: #262626 !important;
  font-size: 14px !important;
  min-width: 80px !important;
  box-sizing: border-box !important;
  text-align: left !important;
}

.ant-table-tbody > tr > td {
  word-wrap: break-word !important;
  word-break: break-all !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  font-size: 14px !important;
  padding: 8px 10px !important;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .container {
    max-width: 100% !important;
    padding: 0 15px !important;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px !important;
  }
  
  /* 移动设备上的表格样式 */
  .ant-table-thead > tr > th {
    font-size: 13px !important;
    padding: 8px 6px !important;
    font-weight: bold !important;
    color: #262626 !important;
    overflow: visible !important;
    text-overflow: unset !important;
    text-align: left !important;
  }
  
  .ant-table-tbody > tr > td {
    font-size: 13px !important;
    padding: 6px 6px !important;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 8px !important;
  }
  
  /* 小屏幕设备的表格优化 */
  .ant-table-thead > tr > th {
    font-size: 12px !important;
    padding: 5px 3px !important;
    text-align: left !important;
  }
  
  .ant-table-tbody > tr > td {
    font-size: 11px !important;
    padding: 4px 3px !important;
  }
}

/* 下钻数据表格样式 */
.drill-down-row {
  background-color: #fafafa !important;
  border-left: 3px solid #1890ff !important;
}

.drill-down-row td {
  color: #666 !important;
  font-size: 13px !important;
}

.drill-down-row:first-of-type {
  border-top: 1px solid #e8e8e8 !important;
}

/* 下钻数据后的原始数据行分隔线 */
.after-drill-down-row {
  border-top: 2px solid #d9d9d9 !important;
}

/* 下钻数据缩进样式 */
.drill-down-indent {
  position: relative;
}

.drill-down-indent::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #d9d9d9;
}