<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="0c59455557e94c028adfaab6a70c30b4" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_0c59455557e94c028adfaab6a70c30b4 = echarts.init(
            document.getElementById('0c59455557e94c028adfaab6a70c30b4'), 'white', {renderer: 'canvas'});
        var option_0c59455557e94c028adfaab6a70c30b4 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "tree",
            "name": "\u8111\u56fe",
            "data": [
                {
                    "name": "\u4e2d\u5fc3\u4e3b\u9898",
                    "children": [
                        {
                            "name": "\u5b50\u4e3b\u9898 A",
                            "children": [
                                {
                                    "name": "\u5b50\u4e3b\u9898 A1"
                                },
                                {
                                    "name": "\u5b50\u4e3b\u9898 A2"
                                }
                            ]
                        },
                        {
                            "name": "\u5b50\u4e3b\u9898 B"
                        }
                    ]
                }
            ],
            "zoom": 1,
            "symbol": "emptyCircle",
            "symbolSize": 7,
            "edgeShape": "curve",
            "edgeForkPosition": "50%",
            "roam": false,
            "expandAndCollapse": true,
            "layout": "orthogonal",
            "orient": "LR",
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "leaves": {},
            "selectedMode": false
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u8111\u56fe\u793a\u4f8b",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_0c59455557e94c028adfaab6a70c30b4.setOption(option_0c59455557e94c028adfaab6a70c30b4);
    </script>
</body>
</html>
