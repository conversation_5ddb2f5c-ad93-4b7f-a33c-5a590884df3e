import React, { useState, useEffect } from 'react';
import { ProgressBar } from 'react-bootstrap';

const ProgressBarIsolated = ({ isLoading, onComplete }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!isLoading) {
      setProgress(0);
      return;
    }

    // 模拟进度条 - 更流畅的非线性动画
    const maxTime = 20000; // 总时长20秒
    const intervalTime = 50; // 每50ms更新一次，更流畅
    const startTime = Date.now();
    
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const timeProgress = Math.min(elapsed / maxTime, 1);
      
      // 使用easeOut曲线：开始快，后面慢
      const easedProgress = 1 - Math.pow(1 - timeProgress, 2);
      const newProgress = Math.min(easedProgress * 99, 99);
      
      setProgress(newProgress);
      
      if (newProgress >= 99) {
        clearInterval(interval);
      }
    }, intervalTime);

    return () => clearInterval(interval);
  }, [isLoading]);

  useEffect(() => {
    if (progress >= 100 && onComplete) {
      onComplete();
    }
  }, [progress, onComplete]);

  if (!isLoading) return null;

  return (
    <div style={{ 
      marginBottom: '20px',
      pointerEvents: 'none',
      position: 'relative',
      zIndex: 1
    }}>
      <ProgressBar
        animated
        now={progress}
        style={{ 
          height: '6px',
          pointerEvents: 'none',
          transition: 'all 0.1s ease-out' // 添加平滑过渡动画
        }}
      />
    </div>
  );
};

export default ProgressBarIsolated; 