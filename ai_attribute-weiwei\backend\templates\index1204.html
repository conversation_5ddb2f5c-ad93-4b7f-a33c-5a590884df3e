<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>归因分析工具</title>
<!--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css">-->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <style>
        body {
            margin: 20px;
        }
        .output-box {
            width: 100%;
            height: 600px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .date-container, .compare-container, .filter-container {
            margin-bottom: 20px;
        }
        .filter-container {
            display: flex;
            gap: 20px;
        }
        .sheet-container {
            margin-top: 20px;
        }
        .sheet-tabs {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">AI归因分析</h1>

<!--        <div style="display: flex; align-items: center; gap: 60px;"> &lt;!&ndash; 增大了 gap 以调整间距 &ndash;&gt;-->
            <!-- 日期选择框 -->
            <div style="display: flex; align-items: center;">
                <label for="tar_date" class="form-label" style="margin-bottom: 0; font-weight: bold; color: #0056b3; margin-right: 10px">
                    目标日期：
                </label>
                <div style="position: relative; width: 200px;">
                    <!-- 自定义默认显示内容 -->
                    <span id="placeholder-text"
                          style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: #999; pointer-events: none; font-size: 14px;">
                        年/月/日
                    </span>
                    <input type="date" id="tar_date" class="form-control"
                           style="
                               width: 100%;
                               border: 2px solid #007bff;
                               border-radius: 5px;
                               padding: 5px 10px;
                               font-size: 14px;
                               color: #0056b3;
                               background-color: #e9f7ff;
                               box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2);
                               transition: border-color 0.3s, box-shadow 0.3s;
                               appearance: none;
                               position: relative;">
                </div>
            </div>

            <!-- 归因指标下拉框 -->
            <div style="display: flex; align-items: center; margin-top: 20px;">
                <label for="attr_index" class="form-label" style="margin-right: 10px; font-weight: bold; color: #0056b3;">归因指标：</label>
                <select id="attr_index" class="form-select"
                        style="width: 150px; border: 2px solid #007bff; border-radius: 5px; padding: 5px 10px; font-size: 14px; color: #0056b3; background-color: #e9f7ff; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                    <option value="GMV">GMV</option>
                </select>
            </div>
<!--        </div>-->

        <!-- 筛选框 -->
        <div class="filter-container" style="margin-top: 20px;">
            <div style="display: flex; align-items: center; gap: 20px;">
                <!-- 子品牌下拉框 -->
                <div style="display: flex; align-items: center;">
                    <label for="sub_brand" class="form-label" style="margin-right: 10px; font-weight: bold; color: #0056b3;">维度筛选：</label>
                    <label for="sub_brand" class="form-label" style="margin-right: 10px; font-weight: bold; color: #0056b3;">子品牌</label>
                    <select id="sub_brand" class="form-select"
                            style="width: 150px; border: 2px solid #007bff; border-radius: 5px; padding: 5px 10px; font-size: 14px; color: #0056b3; background-color: #e9f7ff; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                        <option value="全部">全部</option>
                        <option value="多力多滋">多力多滋</option>
                        <option value="一口阳光">一口阳光</option>
                        <option value="乐事">乐事</option>
                        <option value="百草味">百草味</option>
                        <option value="桂格">桂格</option>
                        <option value="奇多">奇多</option>
                    </select>
                </div>

                <!-- 省份下拉框 -->
                <div style="display: flex; align-items: center;">
                    <label for="province" class="form-label" style="margin-right: 10px; font-weight: bold; color: #0056b3;">省份</label>
                    <select id="province" class="form-select"
                            style="width: 150px; border: 2px solid #007bff; border-radius: 5px; padding: 5px 10px; font-size: 14px; color: #0056b3; background-color: #e9f7ff; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                        <option value="全部">全部</option>
                        <option value="安徽省">安徽省</option>
                        <option value="北京市">北京市</option>
                        <option value="重庆市">重庆市</option>
                        <option value="福建省">福建省</option>
                        <option value="甘肃省">甘肃省</option>
                        <option value="广东省">广东省</option>
                        <option value="广西壮族自治区">广西壮族自治区</option>
                        <option value="贵州省">贵州省</option>
                        <option value="海南省">海南省</option>
                        <option value="河北省">河北省</option>
                        <option value="河南省">河南省</option>
                        <option value="黑龙江省">黑龙江省</option>
                        <option value="湖北省">湖北省</option>
                        <option value="湖南省">湖南省</option>
                        <option value="吉林省">吉林省</option>
                        <option value="江苏省">江苏省</option>
                        <option value="江西省">江西省</option>
                        <option value="辽宁省">辽宁省</option>
                        <option value="内蒙古自治区">内蒙古自治区</option>
                        <option value="宁夏回族自治区">宁夏回族自治区</option>
                        <option value="青海省">青海省</option>
                        <option value="山东省">山东省</option>
                        <option value="山西省">山西省</option>
                        <option value="陕西省">陕西省</option>
                        <option value="上海市">上海市</option>
                        <option value="四川省">四川省</option>
                        <option value="天津市">天津市</option>
                        <option value="西藏自治区">西藏自治区</option>
                        <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
                        <option value="云南省">云南省</option>
                        <option value="浙江省">浙江省</option>
                    </select>
                </div>

                <!-- 零售商下拉框 -->
                <div style="display: flex; align-items: center;">
                    <label for="retailer" class="form-label" style="margin-right: 10px; font-weight: bold; color: #0056b3;">零售商</label>
                    <select id="retailer" class="form-select"
                            style="width: 150px; border: 2px solid #007bff; border-radius: 5px; padding: 5px 10px; font-size: 14px; color: #0056b3; background-color: #e9f7ff; box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2); transition: border-color 0.3s, box-shadow 0.3s;">
                        <option value="全部">全部</option>
                        <option value="711">711</option>
                        <option value="百汇">百汇</option>
                        <option value="大润发">大润发</option>
                        <option value="共橙">共橙</option>
                        <option value="好特卖">好特卖</option>
                        <option value="华润">华润</option>
                        <option value="惠宜选">惠宜选</option>
                        <option value="吉慕">吉慕</option>
                        <option value="佳佳">佳佳</option>
                        <option value="佳美">佳美</option>
                        <option value="家家悦">家家悦</option>
                        <option value="江小囤">江小囤</option>
                        <option value="京东便利店">京东便利店</option>
                        <option value="昆仑好客">昆仑好客</option>
                        <option value="懒猫超市">懒猫超市</option>
                        <option value="零食有鸣">零食有鸣</option>
                        <option value="罗森">罗森</option>
                        <option value="美好超市">美好超市</option>
                        <option value="美团买菜">美团买菜</option>
                        <option value="美宜佳">美宜佳</option>
                        <option value="全家便利店">全家便利店</option>
                        <option value="散店-便利店">散店-便利店</option>
                        <option value="散店-超市">散店-超市</option>
                        <option value="松鼠单体加盟">松鼠单体加盟</option>
                        <option value="万辉">万辉</option>
                        <option value="文峰千家惠">文峰千家惠</option>
                        <option value="沃尔玛">沃尔玛</option>
                        <option value="物美">物美</option>
                        <option value="小柴购">小柴购</option>
                        <option value="熊猫很忙">熊猫很忙</option>
                        <option value="永辉">永辉</option>
                        <option value="优购哆">优购哆</option>
                    </select>
                </div>
            </div>
        </div>


        <!-- 对比类型 -->
        <div class="compare-container">
            <label class="form-label" style="margin-right: 10px; font-weight: bold; color: #0056b3;">对比类型：</label>
            <div class="btn-group" role="group">
                <input type="radio" class="btn-check" name="compare_type" id="compare_huanbi" value="环比" checked>
                <label class="btn btn-outline-primary" for="compare_huanbi">环比</label>

                <input type="radio" class="btn-check" name="compare_type" id="compare_tongbi" value="同比">
                <label class="btn btn-outline-primary" for="compare_tongbi">同比</label>
            </div>
            <!-- 提示文案 -->
            <div class="compare-hint mt-2" style="font-size: 14px; color: #6c757d;">
                <p>环比定义：选定目标日期所属周的上一周同一天，如目标日期为本周五，则环比上一周的周五</p>
                <p>同比定义：选定目标日期所属全年周次+天次的前一年相同周次+天次，如目标日期为本年第28周的周五，则同比去年第28周的周五</p>
            </div>
        </div>

        <div class="container mt-4 d-flex align-items-center">
            <!-- 按钮 -->
            <button id="run_analysis" class="btn btn-primary me-3">AI归因分析</button>

            <!-- 进度条容器 -->
            <div id="progressContainer" class="progress" style="display: none; width: 200px; height: 25px;">
                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%; background-color: #3366FF;"
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    0%
                </div>
            </div>
        </div>

        <!-- 分析结果显示 -->
        <div id="output" class="output-box mt-3"></div>

        <!-- 下载结果按钮 -->
        <button id="download_excel" class="btn btn-secondary mt-3" disabled>下载完整结果</button>

        <!-- 表格显示区域 -->
        <div class="sheet-container">
            <ul class="nav nav-tabs sheet-tabs" id="sheetTabs" role="tablist">
                <!-- 动态生成 Sheet 标签 -->
            </ul>
            <div class="tab-content" id="sheetContent">
                <!-- 动态生成表格 -->
            </div>
        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            $("#run_analysis").click(function () {
                const tar_date = $("#tar_date").val();
                const compare_type = $("input[name='compare_type']:checked").val();
                const sub_brand = $("#sub_brand").val();
                const province = $("#province").val();
                const retailer = $("#retailer").val();

                if (!tar_date) {
                    alert("请选择目标日期！");
                    return;
                }

                // 初始化进度条
                const progressBar = $("#progressBar");
                progressBar.css("width", "0%").text("0%");
                $("#progressContainer").show();

                let progress = 0;
                const maxTime = 20000; // 20秒
                const intervalTime = 200; // 每200ms更新一次
                const step = (100 / (maxTime / intervalTime)); // 每次增加的进度值

                // 匀速推进进度条
                const interval = setInterval(() => {
                    if (progress < 100) {
                        progress += step;
                        progressBar.css("width", `${progress}%`).text(`${Math.floor(progress)}%`);
                    } else {
                        clearInterval(interval); // 进度条满了后停止
                    }
                }, intervalTime);

                // 向后端发送请求
                $.post("/run_attribution", { tar_date, compare_type, sub_brand, province, retailer }, function (response) {
                    clearInterval(interval); // 停止进度条匀速推进

                    // 确保进度条直接到100%
                    progressBar.css("width", "100%").text("100%");

                    // 1秒后隐藏进度条
                    setTimeout(() => {
                        $("#progressContainer").hide();
                    }, 1000);

                    // 处理后端返回结果
                    if (response.status === "success") {
                        // 获取后端返回的 market_data 和 result
                        const marketData = response.market_data || "无大盘数据"; // 防止为空时显示默认提示
                        const analysisResult = response.result || "无分析结果"; // 防止为空时显示默认提示

                        // 函数：处理内容，给带 % 的正数和负数添加颜色
                        function highlightPercentages(text) {
                            return text.replace(/([-+]\d+(\.\d+)?(%)?)/g, function (match) {
                                if (match.includes("-")) {
                                    return `<span style="color: green;">${match}</span>`;
                                } else {
                                    return `<span style="color: red;">${match}</span>`;
                                }
                            });
                        }// 处理大盘数据和分析结果
                        const highlightedMarketData = highlightPercentages(marketData);
                        const highlightedAnalysisResult = highlightPercentages(analysisResult);

                        // 将 market_data 加粗显示，并与 result 组合
                        const outputHtml = `<strong>${highlightedMarketData}<br><br></strong>${highlightedAnalysisResult}`;

                        // 设置到输出文本框
                        $("#output").html(outputHtml);

                        // 更新表格显示
                        displaySheets(response.sheets);

                        // 使下载按钮可用并变蓝
                        $("#download_excel")
                            .removeClass("btn-secondary disabled")
                            .addClass("btn-primary")
                            .prop("disabled", false);
                    } else {
                        $("#output").text(`错误: ${response.message}`);
                    }

                });
            });


            // 下载按钮点击事件
            $("#download_excel").click(function () {
                if (!$(this).hasClass("disabled")) {
                    window.location.href = "/download";
                }
            });

            function displaySheets(sheets) {
                const sheetTabs = $("#sheetTabs");
                const sheetContent = $("#sheetContent");
                sheetTabs.empty();
                sheetContent.empty();

                let isActive = true;

                const dimensionPriority = ["子品牌", "省份", "零售商"];
                const gmvPriority = ["GMV基准值", "GMV当前值", "GMV波动值", "GMV波动率"];

                for (const [sheetName, data] of Object.entries(sheets)) {
                    const tabId = `sheet-${sheetName}`;
                    const tab = $(`
                        <li class="nav-item" role="presentation">
                            <button class="nav-link ${isActive ? "active" : ""}" id="${tabId}-tab" data-bs-toggle="tab" data-bs-target="#${tabId}" type="button" role="tab">
                                ${sheetName}
                            </button>
                        </li>
                    `);
                    sheetTabs.append(tab);

                    const table = $(`<div class="tab-pane fade ${isActive ? "show active" : ""}" id="${tabId}" role="tabpanel"></div>`);
                    if (data.length > 0) {
                        const tableContent = $("<table class='table table-striped'></table>");
                        const thead = $("<thead><tr></tr></thead>");
                        const tbody = $("<tbody></tbody>");

                        // 获取所有列名，并按维度优先级和 GMV 优先级排序
                        const columns = [
                            ...dimensionPriority.filter(col => col in data[0]),
                            ...gmvPriority.filter(col => col in data[0]),
                            ...Object.keys(data[0]).filter(col => !dimensionPriority.includes(col) && !gmvPriority.includes(col))
                        ];

                        // 渲染表头，添加上下箭头排序功能
                        columns.forEach((key, index) => {
                            const th = $(`
                                <th>
                                    ${key}
                                    <span class="sort-arrows">
                                        <span class="arrow-up" style="cursor: pointer;">▲</span>
                                        <span class="arrow-down" style="cursor: pointer;">▼</span>
                                    </span>
                                </th>
                            `);

                            // 添加正序排序事件
                            th.find(".arrow-up").on("click", () => sortTable(tbody, index, key, true));
                            // 添加倒序排序事件
                            th.find(".arrow-down").on("click", () => sortTable(tbody, index, key, false));

                            thead.find("tr").append(th);
                        });

                        // 渲染表格数据
                        data.forEach(row => {
                            const tr = $("<tr></tr>");

                            // 判断变化率值，绿涨红跌
                            const gmvChangeRate = parseFloat(row["GMV变化率"]?.replace(/[%,+]/g, '') || row["GMV波动率"]?.replace(/[%,+]/g, '') || '0');
                            let textColor = "";
                            if (!isNaN(gmvChangeRate)) {
                                if (gmvChangeRate === 0) {
                                    textColor = "#333"; // 变化率为0时显示黑色
                                } else {
                                    textColor = gmvChangeRate > 0 ? "#52c41a" : "#f5222d"; // 绿涨红跌
                                }
                            }

                            // 填充行数据
                            columns.forEach(key => {
                                const cell = $(`<td>${row[key]}</td>`);
                                if (textColor) {
                                    cell.css("color", textColor); // 设置文本颜色
                                }
                                tr.append(cell);
                            });
                            tbody.append(tr);
                        });

                        tableContent.append(thead, tbody);
                        table.append(tableContent);
                    } else {
                        table.append("<p>数据为空</p>");
                    }
                    sheetContent.append(table);

                    isActive = false;
                }
            }

            // 排序函数
            function sortTable(tbody, columnIndex, key, ascending) {
                const rows = tbody.find("tr").toArray();

                rows.sort((a, b) => {
                    const aValue = a.cells[columnIndex]?.textContent.trim() || "";
                    const bValue = b.cells[columnIndex]?.textContent.trim() || "";

                    // 判断是否为数值列
                    const aNum = parseFloat(aValue.replace('%', ''));
                    const bNum = parseFloat(bValue.replace('%', ''));
                    const isNumeric = !isNaN(aNum) && !isNaN(bNum);

                    if (isNumeric) {
                        return ascending ? aNum - bNum : bNum - aNum;
                    } else {
                        return ascending
                            ? aValue.localeCompare(bValue, undefined, { numeric: true })
                            : bValue.localeCompare(aValue, undefined, { numeric: true });
                    }
                });

                // 重新渲染排序后的表格
                tbody.empty().append(rows);
            }

        });
    </script>
</body>
</html>
